function consolidateCOACompanies() {
  // Step 1: Get detailed information about all chart of account companies
  databaseConnection.obj.getAll('chart_of_accounts_companies', function(companies) {
    console.log(`Processing ${companies.length} chart of account companies...`);
    console.log('=== COMPANY DETAILS ===');

    let processedCompanies = 0;
    let companyData = [];

    // Process each company to get its chart of accounts count and details
    companies.forEach(function(company) {
      databaseConnection.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: company.id}, function(chartOfAccounts) {
        const companyInfo = {
          id: company.id,
          name: company.name,
          coaCount: chartOfAccounts.length,
          company: company
        };

        companyData.push(companyInfo);
        console.log(`Company ID: ${company.id} | Name: "${company.name}" | COA Count: ${chartOfAccounts.length}`);

        processedCompanies++;

        // When all companies are processed, analyze the data
        if (processedCompanies === companies.length) {
          console.log('\n=== ANALYSIS ===');
          analyzeCompanies(companyData);
        }
      });
    });
  });
}

function analyzeCompanies(companyData) {
  // Find all IH companies
  const ihCompanies = companyData.filter(c => c.name === 'IH');
  console.log(`Found ${ihCompanies.length} IH companies:`);
  ihCompanies.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });

  // Find all Cherokee Dock companies
  const cherokeeCompanies = companyData.filter(c => c.name === 'Cherokee Dock');
  console.log(`\nFound ${cherokeeCompanies.length} Cherokee Dock companies:`);
  cherokeeCompanies.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });

  // Identify target companies
  // For IH: use the one with most COAs (clearly the main one with 124 COAs)
  const targetIH = ihCompanies.reduce((max, current) =>
    current.coaCount > max.coaCount ? current : max, ihCompanies[0]);

  // For Cherokee Dock: since they all have 1 COA, use the earliest created (oldest)
  const targetCherokee = cherokeeCompanies.reduce((earliest, current) => {
    const earliestDate = new Date(earliest.company.date_created);
    const currentDate = new Date(current.company.date_created);
    return currentDate < earliestDate ? current : earliest;
  }, cherokeeCompanies[0]);

  console.log(`\n=== CONSOLIDATION TARGETS ===`);
  if (targetIH) {
    console.log(`Target IH Company: ID ${targetIH.id} with ${targetIH.coaCount} COAs`);
  }
  if (targetCherokee) {
    console.log(`Target Cherokee Dock Company: ID ${targetCherokee.id} with ${targetCherokee.coaCount} COAs`);
  }

  // Identify orphaned companies
  const orphanedIH = ihCompanies.filter(c => c.id !== targetIH?.id);
  const orphanedCherokee = cherokeeCompanies.filter(c => c.id !== targetCherokee?.id);

  console.log(`\n=== ORPHANED COMPANIES TO CONSOLIDATE ===`);
  console.log(`Orphaned IH companies: ${orphanedIH.length}`);
  orphanedIH.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });

  console.log(`Orphaned Cherokee Dock companies: ${orphanedCherokee.length}`);
  orphanedCherokee.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });

  // Store consolidation data for next step
  window.consolidationData = {
    targetIH: targetIH,
    targetCherokee: targetCherokee,
    orphanedIH: orphanedIH,
    orphanedCherokee: orphanedCherokee
  };

  console.log(`\n=== READY FOR CONSOLIDATION ===`);
  console.log(`Run consolidateOrphanedCompanies(true) to execute consolidation`);
  console.log(`Run consolidateOrphanedCompanies(false) to test without committing changes`);
}

function consolidateOrphanedCompanies(commitChanges = false) {
  if (!window.consolidationData) {
    console.log('ERROR: No consolidation data found. Run consolidateCOACompanies() first.');
    return;
  }

  const { targetIH, targetCherokee, orphanedIH, orphanedCherokee } = window.consolidationData;

  console.log(`\n=== CONSOLIDATION ${commitChanges ? 'EXECUTION' : 'TEST'} ===`);
  console.log(`Commit changes: ${commitChanges}`);

  let totalUpdates = 0;
  let completedUpdates = 0;

  // Calculate total updates needed
  const allOrphaned = [...orphanedIH, ...orphanedCherokee];
  totalUpdates = allOrphaned.reduce((sum, company) => sum + company.coaCount, 0);

  console.log(`Total chart of accounts to update: ${totalUpdates}`);

  // Process IH orphaned companies
  orphanedIH.forEach(orphanedCompany => {
    console.log(`\nProcessing orphaned IH company ${orphanedCompany.id}...`);

    databaseConnection.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: orphanedCompany.id}, function(chartOfAccounts) {
      console.log(`Found ${chartOfAccounts.length} COAs to move from company ${orphanedCompany.id} to target IH ${targetIH.id}`);

      chartOfAccounts.forEach(coa => {
        const originalCompanyId = coa.chart_of_accounts_company;
        const updateObj = {
          id: coa.id,
          chart_of_accounts_company: targetIH.id,
          data_source: originalCompanyId  // Track where this came from
        };

        console.log(`${commitChanges ? 'UPDATING' : 'WOULD UPDATE'} COA ${coa.id} "${coa.name}" from company ${originalCompanyId} to ${targetIH.id}`);

        if (commitChanges) {
          databaseConnection.obj.update('chart_of_accounts', updateObj, function(result) {
            completedUpdates++;
            console.log(`✓ Updated COA ${coa.id} - Progress: ${completedUpdates}/${totalUpdates}`);

            if (completedUpdates === totalUpdates) {
              console.log(`\n=== CONSOLIDATION COMPLETE ===`);
              console.log(`Successfully updated ${completedUpdates} chart of accounts`);
            }
          });
        } else {
          completedUpdates++;
          console.log(`✓ Test complete for COA ${coa.id} - Progress: ${completedUpdates}/${totalUpdates}`);

          if (completedUpdates === totalUpdates) {
            console.log(`\n=== TEST COMPLETE ===`);
            console.log(`Would update ${completedUpdates} chart of accounts`);
            console.log(`Run consolidateOrphanedCompanies(true) to execute changes`);
          }
        }
      });
    });
  });

  // Process Cherokee Dock orphaned companies
  orphanedCherokee.forEach(orphanedCompany => {
    console.log(`\nProcessing orphaned Cherokee Dock company ${orphanedCompany.id}...`);

    databaseConnection.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: orphanedCompany.id}, function(chartOfAccounts) {
      console.log(`Found ${chartOfAccounts.length} COAs to move from company ${orphanedCompany.id} to target Cherokee Dock ${targetCherokee.id}`);

      chartOfAccounts.forEach(coa => {
        const originalCompanyId = coa.chart_of_accounts_company;
        const updateObj = {
          id: coa.id,
          chart_of_accounts_company: targetCherokee.id,
          data_source: originalCompanyId  // Track where this came from
        };

        console.log(`${commitChanges ? 'UPDATING' : 'WOULD UPDATE'} COA ${coa.id} "${coa.name}" from company ${originalCompanyId} to ${targetCherokee.id}`);

        if (commitChanges) {
          databaseConnection.obj.update('chart_of_accounts', updateObj, function(result) {
            completedUpdates++;
            console.log(`✓ Updated COA ${coa.id} - Progress: ${completedUpdates}/${totalUpdates}`);

            if (completedUpdates === totalUpdates) {
              console.log(`\n=== CONSOLIDATION COMPLETE ===`);
              console.log(`Successfully updated ${completedUpdates} chart of accounts`);
            }
          });
        } else {
          completedUpdates++;
          console.log(`✓ Test complete for COA ${coa.id} - Progress: ${completedUpdates}/${totalUpdates}`);

          if (completedUpdates === totalUpdates) {
            console.log(`\n=== TEST COMPLETE ===`);
            console.log(`Would update ${completedUpdates} chart of accounts`);
            console.log(`Run consolidateOrphanedCompanies(true) to execute changes`);
          }
        }
      });
    });
  });
}

function findEmptyCompanies() {
  console.log('\n=== FINDING EMPTY CHART OF ACCOUNT COMPANIES ===');

  // Get all chart of account companies
  databaseConnection.obj.getAll('chart_of_accounts_companies', function(companies) {
    console.log(`Checking ${companies.length} chart of account companies for empty ones...`);

    let processedCompanies = 0;
    let emptyCompanies = [];

    companies.forEach(function(company) {
      databaseConnection.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: company.id}, function(chartOfAccounts) {
        processedCompanies++;

        if (chartOfAccounts.length === 0) {
          emptyCompanies.push({
            id: company.id,
            name: company.name,
            date_created: company.date_created
          });
          console.log(`Empty company found: ID ${company.id} - "${company.name}" (created: ${company.date_created})`);
        }

        // When all companies are processed, show results
        if (processedCompanies === companies.length) {
          console.log(`\n=== EMPTY COMPANIES SUMMARY ===`);
          console.log(`Found ${emptyCompanies.length} empty chart of account companies:`);

          if (emptyCompanies.length > 0) {
            console.log('\nEmpty Company IDs:');
            const emptyIds = emptyCompanies.map(c => c.id);
            console.log(emptyIds);

            console.log('\nEmpty Companies Details:');
            emptyCompanies.forEach(company => {
              console.log(`  - ID: ${company.id}, Name: "${company.name}", Created: ${company.date_created}`);
            });

            // Store for potential cleanup
            window.emptyCompaniesData = emptyCompanies;
            console.log('\nEmpty companies data stored in window.emptyCompaniesData');
          } else {
            console.log('No empty companies found.');
          }
        }
      });
    });
  });
}

function deleteEmptyCompanies(commitChanges = false) {
  console.log(`\n=== ${commitChanges ? 'DELETING' : 'TESTING DELETE OF'} EMPTY COMPANIES ===`);

  if (!window.emptyCompaniesData || window.emptyCompaniesData.length === 0) {
    console.log('No empty companies data found. Run findEmptyCompanies() first.');
    return;
  }

  const emptyIds = window.emptyCompaniesData.map(c => c.id);
  console.log(`${commitChanges ? 'Deleting' : 'Would delete'} ${emptyIds.length} empty companies:`);
  console.log(emptyIds);

  window.emptyCompaniesData.forEach(company => {
    console.log(`${commitChanges ? 'DELETING' : 'WOULD DELETE'}: ID ${company.id} - "${company.name}"`);
  });

  if (commitChanges) {
    databaseConnection.obj.erase(emptyIds, function(result) {
      console.log('\n=== DELETION COMPLETE ===');
      console.log(`Successfully deleted ${emptyIds.length} empty chart of account companies`);
      console.log('Result:', result);

      // Clear the stored data since companies are now deleted
      window.emptyCompaniesData = [];
      console.log('Cleared emptyCompaniesData - companies have been deleted');
    });
  } else {
    console.log('\n=== TEST COMPLETE ===');
    console.log(`Would delete ${emptyIds.length} empty companies`);
    console.log('Run deleteEmptyCompanies(true) to execute deletion');
  }
}

// Run the consolidation analysis
consolidateCOACompanies();
