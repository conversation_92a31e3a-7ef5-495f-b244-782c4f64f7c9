function consolidateCOACompanies() {
  // Step 1: Get detailed information about all chart of account companies
  databaseConnection.obj.getAll('chart_of_accounts_companies', function(companies) {
    console.log(`Processing ${companies.length} chart of account companies...`);
    console.log('=== COMPANY DETAILS ===');

    let processedCompanies = 0;
    let companyData = [];

    // Process each company to get its chart of accounts count and details
    companies.forEach(function(company) {
      databaseConnection.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: company.id}, function(chartOfAccounts) {
        const companyInfo = {
          id: company.id,
          name: company.name,
          coaCount: chartOfAccounts.length,
          company: company
        };

        companyData.push(companyInfo);
        console.log(`Company ID: ${company.id} | Name: "${company.name}" | COA Count: ${chartOfAccounts.length}`);

        processedCompanies++;

        // When all companies are processed, analyze the data
        if (processedCompanies === companies.length) {
          console.log('\n=== ANALYSIS ===');
          analyzeCompanies(companyData);
        }
      });
    });
  });
}

function analyzeCompanies(companyData) {
  // Find all IH companies
  const ihCompanies = companyData.filter(c => c.name === 'IH');
  console.log(`Found ${ihCompanies.length} IH companies:`);
  ihCompanies.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });

  // Find all Cherokee Dock companies
  const cherokeeCompanies = companyData.filter(c => c.name === 'Cherokee Dock');
  console.log(`\nFound ${cherokeeCompanies.length} Cherokee Dock companies:`);
  cherokeeCompanies.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });

  // Identify target companies (ones with most COAs)
  const targetIH = ihCompanies.reduce((max, current) =>
    current.coaCount > max.coaCount ? current : max, ihCompanies[0]);

  const targetCherokee = cherokeeCompanies.reduce((max, current) =>
    current.coaCount > max.coaCount ? current : max, cherokeeCompanies[0]);

  console.log(`\n=== CONSOLIDATION TARGETS ===`);
  if (targetIH) {
    console.log(`Target IH Company: ID ${targetIH.id} with ${targetIH.coaCount} COAs`);
  }
  if (targetCherokee) {
    console.log(`Target Cherokee Dock Company: ID ${targetCherokee.id} with ${targetCherokee.coaCount} COAs`);
  }

  // Identify orphaned companies
  const orphanedIH = ihCompanies.filter(c => c.id !== targetIH?.id);
  const orphanedCherokee = cherokeeCompanies.filter(c => c.id !== targetCherokee?.id);

  console.log(`\n=== ORPHANED COMPANIES TO CONSOLIDATE ===`);
  console.log(`Orphaned IH companies: ${orphanedIH.length}`);
  orphanedIH.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });

  console.log(`Orphaned Cherokee Dock companies: ${orphanedCherokee.length}`);
  orphanedCherokee.forEach(c => {
    console.log(`  - ID: ${c.id}, COA Count: ${c.coaCount}`);
  });
}

// Run the function
exportCOAToCSV();
